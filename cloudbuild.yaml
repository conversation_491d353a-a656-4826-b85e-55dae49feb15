steps:
  # Build backend image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '-t'
      - 'gcr.io/$PROJECT_ID/supplyline-backend:$BUILD_ID'
      - '-t'
      - 'gcr.io/$PROJECT_ID/supplyline-backend:latest'
      - './backend'
    id: 'build-backend'

  # Build frontend image with static backend URL
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '-t'
      - 'gcr.io/$PROJECT_ID/supplyline-frontend:$BUILD_ID'
      - '-t'
      - 'gcr.io/$PROJECT_ID/supplyline-frontend:latest'
      - '--build-arg'
      - 'VITE_API_URL=${_BACKEND_URL}'
      - './frontend'
    id: 'build-frontend'
    waitFor: ['build-backend']

  # Push backend image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - 'gcr.io/$PROJECT_ID/supplyline-backend:$BUILD_ID'
    id: 'push-backend'
    waitFor: ['build-backend']

  # Deploy backend to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'supplyline-backend-production'
      - '--image'
      - 'gcr.io/$PROJECT_ID/supplyline-backend:$BUILD_ID'
      - '--region'
      - '${_REGION}'
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'
      - '--port'
      - '8080'
      - '--memory'
      - '1Gi'
      - '--cpu'
      - '1'
      - '--max-instances'
      - '10'
      - '--set-env-vars'
      - 'FLASK_ENV=production,DB_HOST=/cloudsql/${_CLOUDSQL_INSTANCE},DB_USER=supplyline_user,DB_NAME=supplyline,PYTHONDONTWRITEBYTECODE=1,PYTHONUNBUFFERED=1,CORS_ORIGINS=*'
      - '--set-secrets'
      - 'SECRET_KEY=supplyline-secret-key:latest,DB_PASSWORD=supplyline-db-password:latest'
      - '--set-cloudsql-instances'
      - '${_CLOUDSQL_INSTANCE}'
    id: 'deploy-backend'
    waitFor: ['push-backend']

  # Get backend URL for frontend configuration
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        BACKEND_URL=$$(gcloud run services describe supplyline-backend-production --region=us-west1 --format="value(status.url)")
        echo "Backend URL: $$BACKEND_URL"
        echo "$$BACKEND_URL" > /workspace/backend_url.txt
    id: 'get-backend-url'
    waitFor: ['deploy-backend']

  # Rebuild frontend with actual backend URL
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '-t'
      - 'gcr.io/$PROJECT_ID/supplyline-frontend:$BUILD_ID-final'
      - '-t'
      - 'gcr.io/$PROJECT_ID/supplyline-frontend:latest'
      - '--build-arg'
      - 'VITE_API_URL=$$(cat /workspace/backend_url.txt)'
      - './frontend'
    id: 'rebuild-frontend'
    waitFor: ['get-backend-url']



  # Push final frontend image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - 'gcr.io/$PROJECT_ID/supplyline-frontend:$BUILD_ID-final'
    id: 'push-frontend-final'
    waitFor: ['rebuild-frontend']

  # Deploy frontend to Cloud Run with correct backend URL
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'supplyline-frontend-production'
      - '--image'
      - 'gcr.io/$PROJECT_ID/supplyline-frontend:$BUILD_ID-final'
      - '--region'
      - '${_REGION}'
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'
      - '--port'
      - '80'
      - '--memory'
      - '512Mi'
      - '--cpu'
      - '0.5'
      - '--max-instances'
      - '5'
    id: 'deploy-frontend'
    waitFor: ['push-frontend-final']

  # Update backend CORS configuration with static frontend URL
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        echo "Frontend URL: ${_FRONTEND_URL}"
        echo "Backend URL: ${_BACKEND_URL}"

        # Update backend with correct CORS configuration
        gcloud run services update supplyline-backend-${_ENVIRONMENT} \
          --region=${_REGION} \
          --update-env-vars="FLASK_ENV=production,CORS_ORIGINS=${_FRONTEND_URL},DB_HOST=/cloudsql/${_CLOUDSQL_INSTANCE},DB_USER=supplyline_user,DB_NAME=supplyline,PYTHONDONTWRITEBYTECODE=1,PYTHONUNBUFFERED=1"
    id: 'update-backend-cors'
    waitFor: ['deploy-frontend']

# Substitution variables (values supplied via gcloud or environment)
substitutions:
  _ENVIRONMENT: 'production'
  _REGION: 'us-west1'
  _CLOUDSQL_INSTANCE: 'gen-lang-client-0819985982:us-west1:supplyline-db'
  # Dynamic URLs - will be determined during deployment
  _BACKEND_URL: 'TBD'
  _FRONTEND_URL: 'TBD'

# Build options
options:
  logging: CLOUD_LOGGING_ONLY
  machineType: 'E2_HIGHCPU_8'

# Build timeout
timeout: '1200s'

# Images to be pushed to Container Registry
images:
  - 'gcr.io/$PROJECT_ID/supplyline-backend:$BUILD_ID'
  - 'gcr.io/$PROJECT_ID/supplyline-backend:latest'
  - 'gcr.io/$PROJECT_ID/supplyline-frontend:$BUILD_ID-final'
  - 'gcr.io/$PROJECT_ID/supplyline-frontend:latest'
